# Malda Protocol - Integer Overflow Vulnerability Analysis

## Обзор

Найдена критическая уязвимость Integer Overflow в протоколе Malda, аналогичная Memory Allocator vulnerability. Проблема находится в функции `batchProcess()` контракта `BatchSubmitter.sol`.

## Уязвимость: Integer Overflow в Array Indexing

### Проблемный код

```solidity
// malda-lending/src/mToken/BatchSubmitter.sol:115
function batchProcess(BatchProcessMsg calldata data) external {
    // ...
    bytes[] memory journals = abi.decode(data.journalData, (bytes[]));
    uint256 length = data.initHashes.length;
    
    for (uint256 i = 0; i < length;) {
        bytes[] memory singleJournal = new bytes[](1);
        singleJournal[0] = journals[data.startIndex + i];  // ❌ УЯЗВИМОСТЬ!
        //                         ^^^^^^^^^^^^^^^^^^^
        //                         НЕТ проверки overflow!
        
        unchecked {
            ++i;
        }
    }
}
```

### Суть проблемы

1. **Отсутствие валидации**: `data.startIndex` может быть любым числом
2. **Integer overflow**: `data.startIndex + i` может переполниться
3. **Wrap-around эффект**: Доступ к неожиданным элементам массива

## Аналогия с Memory Allocator Vulnerability

### Memory Allocator Bug (из отчета)
```rust
// Проблемный код в bump allocator:
heap_pos += bytes;  // ❌ БЕЗ проверки overflow!

// Атака:
heap_pos = 0x0c00_0000;           // Текущая позиция
request = u32::MAX - 0x0c00_0000; // Огромный запрос
heap_pos += request;              // = u32::MAX + 1 = 0 (OVERFLOW!)
// Результат: доступ к началу кучи вместо конца
```

### BatchSubmitter Bug (Malda)
```solidity
// Проблемный код в array indexing:
index = data.startIndex + i;  // ❌ БЕЗ проверки overflow!

// Атака:
data.startIndex = uint256.max - 5;  // Огромное число
i = 10;                             // В цикле
index = (uint256.max - 5) + 10;     // = uint256.max + 5 = 4 (OVERFLOW!)
// Результат: доступ к journals[4] вместо огромного индекса
```

### Сравнение уязвимостей

| Аспект | Memory Allocator | BatchSubmitter |
|--------|------------------|----------------|
| **Операция** | `heap_pos += bytes` | `startIndex + i` |
| **Overflow** | Heap pointer wrap-around | Array index wrap-around |
| **Результат** | Доступ к чужой памяти | Доступ к чужим journal'ам |
| **Impact** | Memory corruption | Cross-user data access |

## Механизм атаки

### Пример эксплойта

```solidity
// Атакующий создает BatchProcessMsg:
BatchProcessMsg({
    startIndex: 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFB, // uint256.max - 4
    initHashes: [hash1, hash2, hash3, hash4, hash5, hash6, hash7, hash8],  // 8 элементов
    journalData: abi.encode([journal0, journal1, journal2, journal3]),     // 4 journal'а
    // ... остальные поля
})

// В цикле batchProcess происходит:
// i=0: journals[uint256.max-4 + 0] = journals[uint256.max-4] ✓ (если существует)
// i=1: journals[uint256.max-4 + 1] = journals[uint256.max-3] ✓
// i=2: journals[uint256.max-4 + 2] = journals[uint256.max-2] ✓  
// i=3: journals[uint256.max-4 + 3] = journals[uint256.max-1] ✓
// i=4: journals[uint256.max-4 + 4] = journals[uint256.max] ✓
// i=5: journals[uint256.max-4 + 5] = journals[0] ❌ OVERFLOW! Wrap to start
// i=6: journals[uint256.max-4 + 6] = journals[1] ❌ OVERFLOW!
// i=7: journals[uint256.max-4 + 7] = journals[2] ❌ OVERFLOW!
```

### Сценарии атаки

#### 1. **Cross-Journal Data Access**
```solidity
// journals = [attacker_journal, victim_journal_1, victim_journal_2, admin_journal]
// startIndex = uint256.max - 2, initHashes.length = 6

// Результат overflow:
// journals[uint256.max-2 + 3] = journals[0] = attacker_journal ✓
// journals[uint256.max-2 + 4] = journals[1] = victim_journal_1 ❌ 
// journals[uint256.max-2 + 5] = journals[2] = victim_journal_2 ❌
// Атакующий получает доступ к victim данным!
```

#### 2. **Privilege Escalation**
- Выполнение `mintExternal()` с чужими journal данными
- Обход intended access controls
- Манипуляция операций других пользователей

## Контроль доступа

### Кто может эксплуатировать

```solidity
function batchProcess(BatchProcessMsg calldata data) external {
    if (!rolesOperator.isAllowedFor(msg.sender, rolesOperator.PROOF_FORWARDER())) {
        revert BatchSubmitter_CallerNotAllowed();
    }
    // ❌ НО startIndex НЕ ВАЛИДИРУЕТСЯ!
}
```

**Роль PROOF_FORWARDER** (из конфигурации):
```json
"roleName": "PROOF_FORWARDER",
"accounts": [
    "0xCde13fF278bc484a09aDb69ea1eEd3cAf6Ea4E00",  // Admin
    "0x2693946791da99dA78Ac441abA6D5Ce2Bccd96D3"   // Sequencer
]
```

### Векторы компрометации

1. **Скомпрометированный PROOF_FORWARDER**
2. **Ошибка в off-chain коде Sequencer'а**
3. **Race conditions** в batch processing

## Исправление

### Рекомендуемые проверки

```solidity
function batchProcess(BatchProcessMsg calldata data) external {
    if (!rolesOperator.isAllowedFor(msg.sender, rolesOperator.PROOF_FORWARDER())) {
        revert BatchSubmitter_CallerNotAllowed();
    }
    
    _verifyProof(data.journalData, data.seal);
    bytes[] memory journals = abi.decode(data.journalData, (bytes[]));
    uint256 length = data.initHashes.length;
    
    // ✅ ДОБАВИТЬ ПРОВЕРКИ:
    require(data.startIndex < journals.length, "Invalid startIndex");
    require(data.startIndex <= type(uint256).max - length, "Addition overflow");
    require(data.startIndex + length <= journals.length, "Insufficient journals");
    
    for (uint256 i = 0; i < length;) {
        bytes[] memory singleJournal = new bytes[](1);
        singleJournal[0] = journals[data.startIndex + i];  // ✅ Теперь безопасно
        // ... остальная логика
        
        unchecked {
            ++i;
        }
    }
}
```

## Severity Assessment

- **Severity**: HIGH
- **Impact**: Cross-user data access, privilege escalation
- **Likelihood**: MEDIUM (требует роль PROOF_FORWARDER)
- **CVSS**: 7.5+ (High)

## Заключение

Эта уязвимость демонстрирует важность проверки границ при работе с арифметическими операциями над индексами, особенно когда индексы поступают из внешних источников. Аналогия с Memory Allocator vulnerability показывает, что подобные проблемы могут возникать на разных уровнях абстракции - от системного memory management до application-level array indexing.
