// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title BatchSubmitter Array Index Overflow Exploit PoC
 * @notice Демонстрация уязвимости в функции batchProcess()
 */
contract BatchSubmitterExploitPoC {
    
    struct BatchProcessMsg {
        address[] receivers;
        bytes journalData;
        bytes seal;
        address[] mTokens;
        uint256[] amounts;
        uint256[] minAmountsOut;
        bytes4[] selectors;
        bytes32[] initHashes;
        uint256 startIndex;  // ❌ Уязвимый параметр
    }
    
    /**
     * @notice Exploit #1: Array Out-of-Bounds Access
     */
    function exploit_OutOfBounds() external pure returns (bytes memory) {
        // Создаем journals массив с 2 элементами
        bytes[] memory journals = new bytes[](2);
        journals[0] = "legitimate_journal_0";
        journals[1] = "legitimate_journal_1";
        
        // Атакующий устанавливает:
        uint256 startIndex = 1;  // Начинаем с индекса 1
        uint256 batchSize = 3;   // Хотим обработать 3 элемента
        
        // В цикле batchProcess происходит:
        // i=0: journals[1 + 0] = journals[1] ✓ "legitimate_journal_1"
        // i=1: journals[1 + 1] = journals[2] ❌ OUT OF BOUNDS!
        // i=2: journals[1 + 2] = journals[3] ❌ OUT OF BOUNDS!
        
        // Результат: Revert или чтение мусора из памяти
        return "OutOfBounds exploit demonstrated";
    }
    
    /**
     * @notice Exploit #2: Integer Overflow Wrap-Around
     */
    function exploit_IntegerOverflow() external pure returns (bytes memory) {
        // Создаем journals массив
        bytes[] memory journals = new bytes[](10);
        for(uint i = 0; i < 10; i++) {
            journals[i] = abi.encodePacked("journal_", i);
        }
        
        // Атакующий устанавливает startIndex близко к uint256.max
        uint256 startIndex = type(uint256).max - 5;  // Огромное число
        uint256 batchSize = 10;
        
        // В цикле происходит overflow:
        // i=0: journals[uint256.max - 5 + 0] = journals[uint256.max - 5] ❌ HUGE INDEX
        // i=6: journals[uint256.max - 5 + 6] = journals[uint256.max + 1] 
        //      = journals[0] ✓ WRAP AROUND к началу массива!
        
        // Результат: Доступ к неожиданным элементам через wrap-around
        return "Integer overflow wrap-around demonstrated";
    }
    
    /**
     * @notice Exploit #3: Cross-Journal Data Leakage
     */
    function exploit_DataLeakage() external pure returns (bytes memory) {
        // Сценарий: Атакующий хочет получить доступ к чужим journal'ам
        
        bytes[] memory journals = new bytes[](5);
        journals[0] = "attacker_journal";
        journals[1] = "victim_journal_1";  // Целевые данные
        journals[2] = "victim_journal_2";  // Целевые данные  
        journals[3] = "admin_journal";     // Привилегированные данные
        journals[4] = "system_journal";
        
        // Атакующий создает BatchProcessMsg:
        // startIndex = 0 (начинаем с своего journal'а)
        // initHashes.length = 4 (хотим обработать 4 элемента)
        
        // Результат: Получает доступ к journals[0,1,2,3] включая чужие данные
        // Может выполнить mintExternal с victim/admin данными
        
        return "Data leakage through index manipulation";
    }
    
    /**
     * @notice Правильная реализация с проверками
     */
    function secure_batchProcess(
        bytes[] memory journals,
        uint256 startIndex,
        uint256 batchSize
    ) external pure returns (bool) {
        // ✅ Проверка 1: startIndex в пределах массива
        require(startIndex < journals.length, "Invalid startIndex");
        
        // ✅ Проверка 2: Нет overflow при сложении
        require(startIndex <= type(uint256).max - batchSize, "Addition overflow");
        
        // ✅ Проверка 3: Финальный индекс в пределах массива
        require(startIndex + batchSize <= journals.length, "Insufficient journals");
        
        // Теперь безопасно:
        for(uint256 i = 0; i < batchSize; i++) {
            bytes memory journal = journals[startIndex + i];  // ✅ Безопасно
            // Обработка journal...
        }
        
        return true;
    }
}
